@echo off
echo ========================================
echo JavaFX 应用程序构建脚本
echo ========================================
echo.

echo 步骤 1: 清理之前的构建...
call mvn clean
if %ERRORLEVEL% neq 0 (
    echo 清理失败！
    pause
    exit /b 1
)

echo.
echo 步骤 2: 编译项目...
call mvn compile
if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo 步骤 3: 创建 JAR 包...
call mvn package
if %ERRORLEVEL% neq 0 (
    echo 打包失败！
    pause
    exit /b 1
)

echo.
echo 步骤 4: 创建自定义运行时镜像...
call mvn javafx:jlink
if %ERRORLEVEL% neq 0 (
    echo 创建运行时镜像失败！
    pause
    exit /b 1
)

echo.
echo 步骤 5: 创建原生可执行文件...
call mvn jpackage:jpackage
if %ERRORLEVEL% neq 0 (
    echo 创建可执行文件失败！
    pause
    exit /b 1
)

echo.
echo ========================================
echo 构建完成！
echo ========================================
echo 可执行文件位置: dist\JavaFXApp\JavaFXApp.exe
echo 安装包位置: dist\JavaFXApp-1.0.0.msi
echo.
pause
