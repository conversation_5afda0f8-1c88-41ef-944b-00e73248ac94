/* JavaFX 桌面应用程序样式文件 */

/* 根容器样式 */
.root {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    -fx-font-family: "Microsoft YaHei", "SimSun", sans-serif;
}

/* 标题标签样式 */
.title-label {
    -fx-text-fill: #2c3e50;
    -fx-font-weight: bold;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 2, 0, 0, 1);
}

/* 欢迎标签样式 */
.welcome-label {
    -fx-text-fill: #34495e;
    -fx-text-alignment: center;
    -fx-wrap-text: true;
}

/* 主要按钮样式 */
.primary-button {
    -fx-background-color: linear-gradient(to bottom, #3498db, #2980b9);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 8;
    -fx-border-radius: 8;
    -fx-padding: 12 24 12 24;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 4, 0, 0, 2);
}

.primary-button:hover {
    -fx-background-color: linear-gradient(to bottom, #5dade2, #3498db);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.primary-button:pressed {
    -fx-background-color: linear-gradient(to bottom, #2980b9, #1f618d);
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

/* 次要按钮样式 */
.secondary-button {
    -fx-background-color: linear-gradient(to bottom, #95a5a6, #7f8c8d);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 8;
    -fx-border-radius: 8;
    -fx-padding: 12 24 12 24;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 4, 0, 0, 2);
}

.secondary-button:hover {
    -fx-background-color: linear-gradient(to bottom, #bdc3c7, #95a5a6);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.secondary-button:pressed {
    -fx-background-color: linear-gradient(to bottom, #7f8c8d, #6c7b7d);
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

/* 信息标签样式 */
.info-label {
    -fx-text-fill: #7f8c8d;
    -fx-font-size: 12px;
}

/* VBox容器样式 */
.vbox {
    -fx-spacing: 20;
    -fx-alignment: center;
}

/* HBox容器样式 */
.hbox {
    -fx-spacing: 20;
    -fx-alignment: center;
}

/* ImageView样式 */
.image-view {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 8, 0, 0, 2);
}

/* 动画效果 */
.fade-in {
    -fx-opacity: 0;
}

/* 响应式设计 - 小屏幕适配 */
@media (max-width: 800px) {
    .title-label {
        -fx-font-size: 24px;
    }
    
    .welcome-label {
        -fx-font-size: 14px;
    }
    
    .primary-button, .secondary-button {
        -fx-padding: 10 20 10 20;
        -fx-font-size: 12px;
    }
}
