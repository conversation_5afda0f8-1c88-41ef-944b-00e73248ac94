<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.example</groupId>
    <artifactId>javafx-desktop-app</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <name>JavaFX Desktop Application</name>
    <description>A JavaFX desktop application with custom logo and 1280x720 window</description>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <javafx.version>21.0.1</javafx.version>
        <javafx.maven.plugin.version>0.0.8</javafx.maven.plugin.version>
        <maven.compiler.plugin.version>3.11.0</maven.compiler.plugin.version>
    </properties>

    <dependencies>
        <!-- JavaFX Controls -->
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-controls</artifactId>
            <version>${javafx.version}</version>
            <classifier>win</classifier>
        </dependency>

        <!-- JavaFX FXML -->
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-fxml</artifactId>
            <version>${javafx.version}</version>
            <classifier>win</classifier>
        </dependency>

        <!-- JavaFX Base -->
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-base</artifactId>
            <version>${javafx.version}</version>
            <classifier>win</classifier>
        </dependency>

        <!-- JavaFX Graphics -->
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-graphics</artifactId>
            <version>${javafx.version}</version>
            <classifier>win</classifier>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Maven Compiler Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.plugin.version}</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>

            <!-- JavaFX Maven Plugin -->
            <plugin>
                <groupId>org.openjfx</groupId>
                <artifactId>javafx-maven-plugin</artifactId>
                <version>${javafx.maven.plugin.version}</version>
                <configuration>
                    <mainClass>com.example.javafxapp.JavaFXApp</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <id>default-cli</id>
                        <configuration>
                            <mainClass>com.example.javafxapp.JavaFXApp</mainClass>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Maven Shade Plugin for creating fat JAR -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.4.1</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>com.example.javafxapp.JavaFXApp</mainClass>
                                </transformer>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>
                            </transformers>
                            <finalName>javafx-app-fat</finalName>
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- JPackage Plugin for creating native executables -->
            <plugin>
                <groupId>org.panteleyev</groupId>
                <artifactId>jpackage-maven-plugin</artifactId>
                <version>1.6.0</version>
                <configuration>
                    <name>JavaFXApp</name>
                    <appVersion>${project.version}</appVersion>
                    <vendor>Example Company</vendor>
                    <destination>dist</destination>
                    <module>javafxapp/com.example.javafxapp.JavaFXApp</module>
                    <runtimeImage>target/javafx-runtime</runtimeImage>
                    <javaOptions>
                        <option>-Dfile.encoding=UTF-8</option>
                    </javaOptions>
                    <icon>app-icon.ico</icon>
                    <winMenu>true</winMenu>
                    <winDirChooser>true</winDirChooser>
                    <winShortcut>true</winShortcut>
                    <winPerUserInstall>false</winPerUserInstall>
                </configuration>
                <executions>
                    <execution>
                        <id>win</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jpackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- JavaFX JLink Plugin for creating custom runtime -->
            <plugin>
                <groupId>org.openjfx</groupId>
                <artifactId>javafx-jlink-plugin</artifactId>
                <version>0.1.8</version>
                <configuration>
                    <stripDebug>true</stripDebug>
                    <compress>2</compress>
                    <noHeaderFiles>true</noHeaderFiles>
                    <noManPages>true</noManPages>
                    <launcher>javafx-app</launcher>
                    <jlinkImageName>javafx-runtime</jlinkImageName>
                    <jlinkZipName>javafx-runtime</jlinkZipName>
                    <mainClass>com.example.javafxapp.JavaFXApp</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <id>create-runtime</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jlink</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
