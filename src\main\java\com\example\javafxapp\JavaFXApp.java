package com.example.javafxapp;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.image.Image;
import javafx.stage.Stage;

import java.io.IOException;
import java.io.InputStream;

/**
 * JavaFX主应用程序类
 * 设置窗口大小为1280x720，支持自定义图标
 */
public class JavaFXApp extends Application {

    @Override
    public void start(Stage stage) throws IOException {
        // 加载FXML文件
        FXMLLoader fxmlLoader = new FXMLLoader(JavaFXApp.class.getResource("hello-view.fxml"));
        Parent root = fxmlLoader.load();
        
        // 创建场景，设置窗口大小为1280x720
        Scene scene = new Scene(root, 1280, 720);
        
        // 加载CSS样式文件
        String css = this.getClass().getResource("/css/styles.css").toExternalForm();
        scene.getStylesheets().add(css);
        
        // 设置窗口标题
        stage.setTitle("JavaFX Desktop Application");
        
        // 设置应用程序图标
        try {
            InputStream iconStream = getClass().getResourceAsStream("/images/app-icon.png");
            if (iconStream != null) {
                Image icon = new Image(iconStream);
                stage.getIcons().add(icon);
                iconStream.close();
            }
        } catch (Exception e) {
            System.out.println("无法加载应用程序图标: " + e.getMessage());
        }
        
        // 设置窗口不可调整大小（可选）
        stage.setResizable(true);
        
        // 设置最小窗口大小
        stage.setMinWidth(800);
        stage.setMinHeight(600);
        
        // 设置场景并显示窗口
        stage.setScene(scene);
        stage.show();
    }

    public static void main(String[] args) {
        launch();
    }
}
