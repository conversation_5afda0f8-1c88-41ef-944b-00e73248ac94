package com.example.javafxapp;

import javafx.fxml.FXML;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;

import java.io.InputStream;

/**
 * Hello页面的控制器类
 * 处理用户界面交互逻辑
 */
public class HelloController {
    
    @FXML
    private Label welcomeLabel;
    
    @FXML
    private Button clickButton;
    
    @FXML
    private ImageView logoImageView;
    
    private int clickCount = 0;

    /**
     * 初始化方法，在FXML加载完成后自动调用
     */
    @FXML
    private void initialize() {
        // 设置欢迎文本
        welcomeLabel.setText("欢迎使用 JavaFX 桌面应用程序！");
        
        // 加载并设置Logo图片
        loadLogo();
        
        // 设置按钮初始文本
        clickButton.setText("点击我！");
    }
    
    /**
     * 加载Logo图片
     */
    private void loadLogo() {
        try {
            InputStream logoStream = getClass().getResourceAsStream("/images/logo.png");
            if (logoStream != null) {
                Image logo = new Image(logoStream);
                logoImageView.setImage(logo);
                logoImageView.setFitWidth(200);
                logoImageView.setFitHeight(200);
                logoImageView.setPreserveRatio(true);
                logoStream.close();
            } else {
                // 如果没有找到logo图片，隐藏ImageView
                logoImageView.setVisible(false);
            }
        } catch (Exception e) {
            System.out.println("无法加载Logo图片: " + e.getMessage());
            logoImageView.setVisible(false);
        }
    }
    
    /**
     * 按钮点击事件处理方法
     */
    @FXML
    private void onButtonClick() {
        clickCount++;
        
        if (clickCount == 1) {
            welcomeLabel.setText("你好！感谢你的第一次点击！");
            clickButton.setText("再点击一次");
        } else if (clickCount == 2) {
            welcomeLabel.setText("太棒了！你已经点击了 " + clickCount + " 次！");
            clickButton.setText("继续点击");
        } else {
            welcomeLabel.setText("你已经点击了 " + clickCount + " 次！继续探索吧！");
            clickButton.setText("点击次数: " + clickCount);
        }
    }
    
    /**
     * 重置按钮点击事件处理方法
     */
    @FXML
    private void onResetClick() {
        clickCount = 0;
        welcomeLabel.setText("欢迎使用 JavaFX 桌面应用程序！");
        clickButton.setText("点击我！");
    }
}
