# 图标文件说明

## 需要的图标文件

请在此目录下放置以下PNG格式的图标文件：

### 1. app-icon.png
- **用途**: 应用程序窗口图标（显示在任务栏和窗口标题栏）
- **建议尺寸**: 32x32, 48x48, 64x64, 128x128 像素（可以是其中任意一种）
- **格式**: PNG
- **透明背景**: 建议使用透明背景

### 2. logo.png  
- **用途**: 应用程序内部显示的Logo
- **建议尺寸**: 200x200 像素或更大（程序会自动缩放到200x200）
- **格式**: PNG
- **透明背景**: 可选，根据设计需要

## 图标制作建议

1. **设计风格**: 简洁、现代、易识别
2. **颜色**: 使用与应用程序主题相符的颜色
3. **分辨率**: 使用高分辨率图片以确保在不同屏幕上显示清晰
4. **文件大小**: 建议单个文件不超过1MB

## 如何添加图标

1. 将准备好的PNG图标文件复制到此目录
2. 确保文件名正确：
   - `app-icon.png` - 应用程序图标
   - `logo.png` - 应用程序Logo
3. 重新编译并运行应用程序

## 注意事项

- 如果没有提供图标文件，应用程序仍然可以正常运行，只是不会显示自定义图标
- 图标文件路径区分大小写
- 建议使用专业的图标设计工具制作图标，如Adobe Illustrator、Figma等

## 示例图标规格

```
app-icon.png:
- 尺寸: 64x64 像素
- 格式: PNG-24 with alpha
- 背景: 透明

logo.png:
- 尺寸: 256x256 像素
- 格式: PNG-24 with alpha
- 背景: 透明或白色
```
