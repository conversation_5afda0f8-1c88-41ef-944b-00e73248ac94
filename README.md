# JavaFX 桌面应用程序

一个使用JavaFX开发的Windows桌面应用程序，具有自定义图标支持和现代化界面设计。

## 功能特性

- ✅ **自定义图标支持**: 支持PNG格式的应用程序图标和Logo
- ✅ **固定窗口大小**: 1280x720像素的主窗口
- ✅ **Hello页面**: 简洁友好的欢迎界面
- ✅ **现代化UI**: 使用CSS样式美化界面
- ✅ **响应式交互**: 按钮点击计数和重置功能
- ✅ **中文支持**: 完整的中文界面

## 技术栈

- **Java**: 17+
- **JavaFX**: 21.0.1
- **Maven**: 项目构建工具
- **FXML**: 界面布局
- **CSS**: 样式美化

## 项目结构

```
javafx-desktop-app/
├── src/
│   └── main/
│       ├── java/
│       │   ├── module-info.java
│       │   └── com/example/javafxapp/
│       │       ├── JavaFXApp.java          # 主应用程序类
│       │       └── HelloController.java    # Hello页面控制器
│       └── resources/
│           ├── com/example/javafxapp/
│           │   └── hello-view.fxml         # Hello页面布局
│           ├── css/
│           │   └── styles.css              # 样式文件
│           └── images/                     # 图标目录
│               └── README.md               # 图标使用说明
├── pom.xml                                 # Maven配置文件                                
└── README.md                               # 项目说明文件
```

## 快速开始

### 环境要求

1. **Java 17或更高版本**
   ```bash
   java --version
   ```

2. **Maven 3.6+**
   ```bash
   mvn --version
   ```

### 运行应用程序

#### 方法1: 使用运行脚本（推荐）
```bash
run.bat
```

#### 方法2: 使用Maven命令
```bash
# 编译项目
mvn clean compile

# 运行应用程序
mvn javafx:run
```

#### 方法3: 打包运行
```bash
# 打包项目
mvn clean package

# 运行JAR文件（需要JavaFX运行时）
java --module-path "path/to/javafx/lib" --add-modules javafx.controls,javafx.fxml -jar target/javafx-desktop-app-1.0.0.jar
```

## 自定义图标

1. 准备PNG格式的图标文件：
   - `app-icon.png`: 应用程序窗口图标（建议32x32或64x64像素）
   - `logo.png`: 应用程序内部Logo（建议200x200像素或更大）

2. 将图标文件放置到 `src/main/resources/images/` 目录下

3. 重新编译并运行应用程序

详细的图标制作和使用说明请参考：`src/main/resources/images/README.md`

## 开发说明

### 修改窗口大小
在 `JavaFXApp.java` 中修改以下代码：
```java
Scene scene = new Scene(root, 1280, 720); // 修改这里的数值
```

### 添加新页面
1. 在 `src/main/resources/com/example/javafxapp/` 下创建新的FXML文件
2. 在 `src/main/java/com/example/javafxapp/` 下创建对应的控制器类
3. 在主应用程序中加载新页面

### 修改样式
编辑 `src/main/resources/css/styles.css` 文件来自定义界面样式。

## 常见问题

### Q: 应用程序无法启动
A: 请检查：
- Java版本是否为17+
- Maven是否正确安装
- 是否在项目根目录下运行命令

### Q: 图标不显示
A: 请确认：
- 图标文件格式为PNG
- 文件名正确（app-icon.png, logo.png）
- 文件放置在正确的目录下

### Q: 中文显示乱码
A: 确保：
- 系统支持中文字体
- IDE编码设置为UTF-8
- 项目编译时使用UTF-8编码

## 许可证

本项目采用MIT许可证，详情请参考LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
